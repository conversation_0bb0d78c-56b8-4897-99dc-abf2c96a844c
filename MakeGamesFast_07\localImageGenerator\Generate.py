import torch
from diffusers import DDPMPipeline

# Load the pipeline (force PyTorch weights)
pipe = DDPMPipeline.from_pretrained(
    "google/ddpm-cifar10-32",
    torch_dtype=torch.float32,
    use_safetensors=False
)
unet = pipe.unet.cpu().eval()

# Dummy inputs
dummy_sample   = torch.randn(1, 3, 32, 32)
dummy_timestep = torch.tensor([0], dtype=torch.long)

# Export with opset_version=14 so scaled_dot_product_attention is emitted properly
torch.onnx.export(
    unet,
    (dummy_sample, dummy_timestep),
    "ddpm_cifar10_unet.onnx",
    opset_version=14,
    input_names=["sample","timestep"],
    output_names=["pred_noise"],
    dynamic_axes={
      "sample":    {0:"batch"},
      "timestep":  {0:"batch"},
      "pred_noise":{0:"batch"}
    }
)
print("✅ Exported ONNX (opset 14)")
