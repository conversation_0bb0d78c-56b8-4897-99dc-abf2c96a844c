using UnityEngine;
using LLMUnity;
using System.Threading.Tasks;
using UnityEngine.UI;
using Sirenix.OdinInspector;
public class combine2 : MonoBehaviour
{
    [Required] public LLMCharacter llmCharacter;
    private bool warmUpDone = false;
    private bool isLLMGenerating = false;
    [Required] public Button combineButton;
    [Required] public ThingDatabase thingDatabase;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        SetCombineButtonClickable(); _ = llmCharacter.Warmup(WarmUpCallback);
    }
    public void WarmUpCallback()
    {
        warmUpDone = true;
        Debug.Log("Warmup Done");
        //listleft.OnEntrySelectedCallback += SetCombineButtonClickable;
        //listright.OnEntrySelectedCallback += SetCombineButtonClickable;
        combineButton.onClick.AddListener(CombineElements);
        SetCombineButtonClickable();
    }

    ThingIngredients GetSelectedThingIngredients()
    {
        if (listleft.SelectedEntry != null && listright.SelectedEntry != null)
        {
            return new ThingIngredients(listleft.SelectedEntry.Text, listright.SelectedEntry.Text);
        }
        return new ThingIngredients("", "");
    }

    void CombineElements()
    {
        ThingIngredients ingredients = GetSelectedThingIngredients();
        string message = ingredients.ToString();
        if (ingredients.IsValid() == false)
        {
            Debug.Log("No elements selected");
            return;
        }
        isLLMGenerating = true;
        SetCombineButtonClickable();
        Task chatTask = llmCharacter.Chat(message, GenerateNewElement, () => isLLMGenerating = false);
    }

    public void SetCombineButtonClickable()
    {
        if (listleft.SelectedEntry != null && listright.SelectedEntry != null && isLLMGenerating == false && warmUpDone == true)
        {
            combineButton.SetEnabled(true);
        }
        else
        {
            combineButton.SetEnabled(false);
        }
    }

    public void GenerateNewElement(string text)
    {
        ThingIngredients ingredients = GetSelectedThingIngredients();
        Debug.Log("New element: " + text + " from " + ingredients.ToString());

        // List<ThingData> matchingEntries = thingDatabase.entries.Where(x => x.text == text).ToList();
        // if (matchingEntries.Where(x => x.HasIngredient(ingredients.ingredient1, ingredients.ingredient2)).Count() > 0)
        // {
        //     matchingEntries.ForEach(x => x.AddIngredient(ingredients.ingredient1, ingredients.ingredient2));
        //     return;
        // }

        thingDatabase.Add(new ThingData(thingDatabase.defaultIcon, text));
    }

    bool onValidateWarning = true;
    void OnValidate()
    {
        if (onValidateWarning && !llmCharacter.remote && llmCharacter.llm != null && llmCharacter.llm.model == "")
        {
            Debug.LogWarning($"Please select a model in the {llmCharacter.llm.gameObject.name} GameObject!");
            onValidateWarning = false;
        }
    }
}
