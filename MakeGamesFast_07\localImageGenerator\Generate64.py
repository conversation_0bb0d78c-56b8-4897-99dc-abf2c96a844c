# Generate64.py
import sys
from diffusers import StableDiffusionPipeline

def main():
    if len(sys.argv) < 2:
        print("Usage: python Generate64.py \"your prompt here\"")
        sys.exit(1)

    prompt = sys.argv[1]
    out_path = "out64.png"

    # Load in full precision on CPU
    pipe = StableDiffusionPipeline.from_pretrained(
        "hf-internal-testing/tiny-stable-diffusion-torch"
    )
    pipe = pipe.to("cpu")

    # Generate a 64×64 image
    image = pipe(
        prompt,
        height=64,
        width=64,
        num_inference_steps=25,
        guidance_scale=7.5
    ).images[0]

    image.save(out_path)
    print(f"✨ Saved image to '{out_path}'")

if __name__ == "__main__":
    main()
