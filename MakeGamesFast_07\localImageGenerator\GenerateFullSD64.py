# GenerateFullSD64.py
import sys, torch
from diffusers import StableDiffusionPipeline, EulerDiscreteScheduler
from PIL import Image

def main():
    if len(sys.argv) < 2:
        print("Usage: python GenerateFullSD64.py \"your prompt\" [steps] [guidance] [size]")
        sys.exit(1)

    prompt = sys.argv[1]
    steps  = int(sys.argv[2]) if len(sys.argv) > 2 else 30       # default 30 steps
    scale  = float(sys.argv[3]) if len(sys.argv) > 3 else 7.5    # default 7.5
    size   = int(sys.argv[4]) if len(sys.argv) > 4 else 128     # default 128→downscale to 64
    out    = f"out{size}_{prompt.replace(' ', '_')}_{steps}_{scale}.png"

    # 1) Load the pipeline in FP16 if we have CUDA, else FP32
    dtype = torch.float16 if torch.cuda.is_available() else torch.float32
    pipe = StableDiffusionPipeline.from_pretrained(
        "runwayml/stable-diffusion-v1-5",
        torch_dtype=dtype,
        use_safetensors=True,
        safety_checker=None
    )

    # 2) Swap to a faster scheduler
    pipe.scheduler = EulerDiscreteScheduler.from_config(pipe.scheduler.config)

    # 3) Device + performance tricks
    device = "cuda" if torch.cuda.is_available() else "cpu"
    if device == "cuda":
        props = torch.cuda.get_device_properties(0)
        print(f"🚀 Running on GPU: {torch.cuda.get_device_name(0)} (Compute Capability {props.major}.{props.minor})")
        pipe = pipe.to("cuda")
        try:
            pipe.enable_xformers_memory_efficient_attention()
        except:
            pass
    else:
        print("🐢 Running on CPU (FP32)")
        pipe = pipe.to("cpu")
        pipe.enable_attention_slicing()

    # 4) (Optional) compile the UNet for a one-time speed boost (PyTorch ≥2.0, CC ≥7.0)
    if device == "cuda" and props.major >= 7:
        try:
            pipe.unet = torch.compile(pipe.unet)
            print("🪄 UNet compiled with torch.compile")
        except Exception as e:
            print("⚠️ torch.compile failed:", e)
    else:
        print("ℹ️ Skipping torch.compile on this GPU (CC < 7.0 or CPU)")

    # 5) Generate at `size`×`size`
    img = pipe(
        prompt=prompt,
        height=size,
        width=size,
        num_inference_steps=steps,
        guidance_scale=scale
    ).images[0]

    # 6) Downscale to 64×64 if needed
    #if size != 64:
    #    img = img.resize((64, 64), resample=Image.LANCZOS)

    # 7) Save
    img.save(out)
    print(f"✨ Saved '{out}' (steps={steps}, guidance={scale}, gen_size={size})")

if __name__ == "__main__":
    main()
