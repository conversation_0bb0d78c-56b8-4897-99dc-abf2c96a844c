using UnityEngine;
using System.Collections.Generic;
using System;

[CreateAssetMenu(fileName = "ThingDatabase", menuName = "Game/Thing Database")]
public class ThingDatabase : ScriptableObject
{
    [Tooltip("The list of all available things.")]
    public List<ThingData> entries = new List<ThingData>();
    public Action OnDatabaseChanged;

    public void Add(ThingData data)
    {
        entries.Add(data);
        OnDatabaseChanged?.Invoke();
    }
    public void Remove(ThingData data)
    {
        entries.Remove(data);
        OnDatabaseChanged?.Invoke();
    }
    public void Clear()
    {
        entries.Clear();
        OnDatabaseChanged?.Invoke();
    }
}