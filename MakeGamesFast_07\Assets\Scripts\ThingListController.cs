using System;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;
using Unity.VisualScripting;

public class ThingListController : MonoBehaviourSingleton<ThingListController>
{
    // runtime
    [Required] public VariableGridLayout ContentContainer;
    [Required] public ThingDatabase thingDatabase;
    public ThingEntryVisual FirstSelectedEntry => _firstSelectedEntry;
    private ThingEntryVisual _firstSelectedEntry;
    public ThingEntryVisual SecondSelectedEntry => _secondSelectedEntry;
    private ThingEntryVisual _secondSelectedEntry;
    public Action OnSelectionChangedCallback;

    public List<ThingEntryVisual> ThingEntryVisuals = new List<ThingEntryVisual>();

    void Awake()
    {
        _firstSelectedEntry = null;
        _secondSelectedEntry = null;

        thingDatabase.OnDatabaseChanged += () => Populate(thingDatabase.entries);
        Populate(thingDatabase.entries);
    }

    /// <summary>
    /// Populate the list with the given data.
    /// Clears the old items and adds new ones.
    /// </summary>
    public void Populate(List<ThingData> thingDatas)
    {
        ThingEntryVisual[] children = ContentContainer.transform.GetComponentsInChildren<ThingEntryVisual>();

        foreach (ThingEntryVisual child in children)
        {
            if (child != ContentContainer.transform)
            {
                child.OnClickDown = null;
                Destroy(child.gameObject);
            }
        }

        ContentContainer.BeginLayoutUpdate();

        foreach (ThingData thingData in thingDatas)
        {
            // clone your UXML <ThingEntry>
            ThingEntryVisual thingEntry = new ThingEntryVisual.Builder(thingData, ContentContainer.gameObject).Build();

            // hook up selection callback
            thingEntry.OnClickDown += () => OnEntrySelected(thingEntry);

            // add to scrollable list
            ThingEntryVisuals.Add(thingEntry);

            _firstSelectedEntry = null;
            _secondSelectedEntry = null;
            OnSelectionChangedCallback?.Invoke();
        }
        ContentContainer.EndLayoutUpdate();
    }

    //Implment Selection handling
    public void OnEntrySelected(ThingEntryVisual thingEntry)
    {
        thingEntry.
    }
}
