using System.Collections.Generic;
using System;

[System.Serializable]
public class ThingData
{
    public string text;
    public List<ThingIngredients> ingredients;
    public Action OnIngredientsChanged;

    public ThingData(string text)
    {
        this.text = text;
    }
    public ThingData(string text, string ingredient1, string ingredient2)
    {
        this.text = text;
        ingredients.Add(new ThingIngredients(ingredient1, ingredient2));
        OnIngredientsChanged?.Invoke();
    }

    public void AddIngredient(string ingredient1, string ingredient2)
    {
        ingredients.Add(new ThingIngredients(ingredient1, ingredient2));
        OnIngredientsChanged?.Invoke();
    }

    public bool HasIngredient(string ingredient1, string ingredient2)
    {
        foreach (ThingIngredients ingredient in ingredients)
        {
            if ((ingredient.ingredient1 == ingredient1 && ingredient.ingredient2 == ingredient2) || (ingredient.ingredient1 == ingredient2 && ingredient.ingredient2 == ingredient1)) return true;
        }
        return false;
    }
}
