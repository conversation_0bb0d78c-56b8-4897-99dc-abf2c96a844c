[System.Serializable]
public struct ThingIngredients
{
    public string ingredient1;
    public string ingredient2;

    public ThingIngredients(string ingredient1, string ingredient2)
    {
        this.ingredient1 = ingredient1;
        this.ingredient2 = ingredient2;
    }
    public bool IsValid()
    {
        return ingredient1 != "" && ingredient2 != "";
    }

    public override string ToString()
    {
        return ingredient1 + " + " + ingredient2;
    }
    public override bool Equals(object obj)
    {
        if (obj is ThingIngredients)
        {
            ThingIngredients other = (ThingIngredients)obj;
            return (ingredient1 == other.ingredient1 && ingredient2 == other.ingredient2) || (ingredient1 == other.ingredient2 && ingredient2 == other.ingredient1);
        }
        return false;
    }
    public override int GetHashCode()
    {
        return ingredient1.GetHashCode() ^ ingredient2.GetHashCode();
    }
}