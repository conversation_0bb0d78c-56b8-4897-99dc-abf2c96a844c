using UnityEngine;
using UnityEngine.EventSystems;
using System;
using TMPro;

public class ThingEntryVisual : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler, IPointerDownHandler
{
    private ThingData _data;
    public ThingData Data
    {
        get
        {
            return _data;
        }
        set
        {
            if (_data != value)
            {
                _data = value;
                DataChanged?.Invoke();
            }
        }
    }
    public Action DataChanged;
    private TextMeshProUGUI NameText;
    private TextMeshProUGUI CountText;
    private GameObject NewIcon;

    public bool isSelected;
    public bool isHovered;

    public void UpdateVisuals()
    {
        // Update the visuals based on the data
        UpdateTextVisuals();
        NewIcon.SetActive(Data.ingredients.Count == 0);
        UpdateStateVisuals();
    }
    public void UpdateTextVisuals()
    {
        // Update the visuals based on the data
        NameText.text = Data.text;
        CountText.text = Data.ingredients.Count.ToString();
    }
    public void UpdateStateVisuals()
    {
        // Update the visuals based on the state
        if (isSelected)
        {
            // Add your selection logic here
        }
        else if (isHovered)
        {
            // Add your hover logic here
        }
        else
        {
            // Add your default state logic here
        }
    }

    // Events for hover and click interactions
    public Action OnHoverEnter;
    public Action OnHoverLeave;
    public Action OnClickDown;

    // Unity Event Interface Implementations
    public void OnPointerEnter(PointerEventData eventData)
    {
        isHovered = true;
        OnHoverEnter?.Invoke();
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        isHovered = false;
        OnHoverLeave?.Invoke();
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        isSelected = !isSelected;
        OnClickDown?.Invoke();
    }

    public class Builder
    {
        public ThingData data;
        public GameObject container;

        public Builder(ThingData data, GameObject container)
        {
            this.data = data;
            this.container = container;
        }

        public ThingEntryVisual Build()
        {
            if (this.data == null)
            {
                this.data = new ThingData("");
            }
            GameObject thingEntryObject = Instantiate(Game.Instance.thingVisualObjectPrefab, container.transform);
            ThingEntryVisual thingEntry = thingEntryObject.GetComponent<ThingEntryVisual>();
            thingEntry.Data = this.data;

            thingEntry.NameText = thingEntryObject.transform.Find("NameText").GetComponent<TextMeshProUGUI>();
            thingEntry.CountText = thingEntryObject.transform.Find("CountText").GetComponent<TextMeshProUGUI>();
            thingEntry.NewIcon = thingEntryObject.transform.Find("NewIcon").gameObject;

            thingEntry.UpdateVisuals();

            // Example: Hook up event callbacks (optional)
            // thingEntry.OnHoverEnter += () => Debug.Log($"Hovered over: {thingEntry.Data.text}");
            // thingEntry.OnHoverLeave += () => Debug.Log($"Stopped hovering: {thingEntry.Data.text}");
            // thingEntry.OnClickDown += () => Debug.Log($"Clicked: {thingEntry.Data.text}");

            return thingEntry;
        }
    }
}
