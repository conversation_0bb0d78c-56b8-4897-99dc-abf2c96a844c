# THIRD-PARTY SOFTWARE NOTICES AND INFORMATION

This package contains third-party software components governed by the license(s) indicated below:

## Software

The following software libraries are used for the LLM functionality:

### llama.cpp

Developer: llama.cpp<br>
Origin: [link](https://github.com/ggerganov/llama.cpp)<br>
License Type: "MIT"<br>
License: [link](https://github.com/ggerganov/llama.cpp/blob/master/LICENSE)

### llamafile

Developer: Mozilla Ocho<br>
Origin: [link](https://github.com/Mozilla-Ocho/llamafile)<br>
License Type: "Apache 2.0"<br>
License: [link](https://github.com/Mozilla-Ocho/llamafile/blob/main/LICENSE)

### USearch

Developer: Unum Cloud<br>
Origin: [link](https://github.com/unum-cloud/usearch)<br>
License Type: "Apache 2.0"<br>
License: [link](https://github.com/unum-cloud/usearch/blob/main/LICENSE)<br>
Modifications: [link](ThirdParty/usearch/README.md)

---

## Models

The following models can be downloaded with LLMUnity:

### meta-llama/Meta-Llama-3.1-8B-Instruct

Developer: Meta<br>
Origin: [link](https://huggingface.co/meta-llama/Meta-Llama-3.1-8B-Instruct)<br>
Quantization: [link](https://huggingface.co/bartowski/Meta-Llama-3.1-8B-Instruct-GGUF)<br>
License Type: "llama3.1"<br>
License: [link](https://huggingface.co/meta-llama/Meta-Llama-3.1-8B/blob/main/LICENSE)

### meta-llama/Llama-3.2-1B-Instruct

Developer: Meta<br>
Origin: [link](https://huggingface.co/meta-llama/Llama-3.2-1B-Instruct)<br>
Quantization: [link](https://huggingface.co/hugging-quants/Llama-3.2-1B-Instruct-Q4_K_M-GGUF)<br>
License Type: "llama3.2"<br>
License: [link](https://huggingface.co/meta-llama/Llama-3.2-1B-Instruct/blob/main/LICENSE.txt)

### meta-llama/Llama-3.2-3B-Instruct

Developer: Meta<br>
Origin: [link](https://huggingface.co/meta-llama/Llama-3.2-3B-Instruct)<br>
Quantization: [link](https://huggingface.co/hugging-quants/Llama-3.2-3B-Instruct-Q4_K_M-GGUF)<br>
License Type: "llama3.2"<br>
License: [link](https://huggingface.co/meta-llama/Llama-3.2-3B-Instruct/blob/main/LICENSE.txt)

### google/gemma-2-9b-it

Developer: Google<br>
Origin: [link](https://huggingface.co/google/gemma-2-9b-it)<br>
Quantization: [link](https://huggingface.co/bartowski/gemma-2-9b-it-GGUF)<br>
License Type: "gemma"<br>
License: [link](https://ai.google.dev/gemma/terms)

<br>

### mistralai/Mistral-7B-Instruct-v0.2

Developer: Mistral AI<br>
Origin: [link](https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2)<br>
Quantization: [link](https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.2-GGUF)<br>
License Type: "Apache 2.0"<br>
License: [link](https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2)

<br>

### microsoft/Phi-3-mini-4k-instruct-gguf

Developer: Microsoft<br>
Origin: [link](https://huggingface.co/microsoft/Phi-3.5-mini-instruct)<br>
Quantization: [link](https://huggingface.co/bartowski/Phi-3.5-mini-instruct-GGUF)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/microsoft/Phi-3.5-mini-instruct/resolve/main/LICENSE)

<br>

### teknium/OpenHermes-2.5-Mistral-7B

Developer: teknium<br>
Origin: [link](https://huggingface.co/teknium/OpenHermes-2.5-Mistral-7B)<br>
Quantization: [link](https://huggingface.co/TheBloke/OpenHermes-2.5-Mistral-7B-GGUF)<br>
License Type: "Apache 2.0"<br>
License: [link](https://huggingface.co/teknium/OpenHermes-2.5-Mistral-7B)

<br>

### Qwen/Qwen2-0.5B-Instruct-GGUF

Developer: Qwen<br>
Origin: [link](https://huggingface.co/Qwen/Qwen2-0.5B-Instruct-GGUF)<br>
Quantization: [link](https://huggingface.co/Qwen/Qwen2-0.5B-Instruct-GGUF)<br>
License Type: "Apache 2.0"<br>
License: [link](https://huggingface.co/Qwen/Qwen2-0.5B-Instruct-GGUF/blob/main/LICENSE)

<br>

### BAAI/bge-large-en-v1.5

Developer: BAAI<br>
Origin: [link](https://huggingface.co/BAAI/bge-large-en-v1.5)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/BAAI/bge-large-en-v1.5)

##### modified by: CompendiumLabs/bge-large-en-v1.5-gguf

Developer: Compendium Labs<br>
Origin: [link](https://huggingface.co/CompendiumLabs/bge-large-en-v1.5-gguf)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/CompendiumLabs/bge-large-en-v1.5-gguf)

<br>

### BAAI/bge-base-en-v1.5

Developer: BAAI<br>
Origin: [link](https://huggingface.co/BAAI/bge-base-en-v1.5)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/BAAI/bge-base-en-v1.5)

##### modified by: CompendiumLabs/bge-base-en-v1.5-gguf

Developer: Compendium Labs<br>
Origin: [link](https://huggingface.co/CompendiumLabs/bge-base-en-v1.5-gguf)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/CompendiumLabs/bge-base-en-v1.5-gguf)

<br>

### BAAI/bge-small-en-v1.5

Developer: BAAI<br>
Origin: [link](https://huggingface.co/BAAI/bge-small-en-v1.5)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/BAAI/bge-small-en-v1.5)

##### modified by: CompendiumLabs/bge-small-en-v1.5-gguf

Developer: Compendium Labs<br>
Origin: [link](https://huggingface.co/CompendiumLabs/bge-small-en-v1.5-gguf)<br>
License Type: "MIT"<br>
License: [link](https://huggingface.co/CompendiumLabs/bge-small-en-v1.5-gguf)

<br>

### sentence-transformers/all-MiniLM-L12-v2

Developer: Sentence Transformers<br>
Origin: [link](https://huggingface.co/sentence-transformers/all-MiniLM-L12-v2)<br>
License Type: "Apache 2.0"<br>
License: [link](https://huggingface.co/sentence-transformers/all-MiniLM-L12-v2)

##### modified by: leliuga/all-MiniLM-L12-v2-GGUF

Developer: Leliuga<br>
Origin: [link](https://huggingface.co/leliuga/all-MiniLM-L12-v2-GGUF)<br>
License Type: "Apache 2.0"<br>
License: [link](https://huggingface.co/leliuga/all-MiniLM-L12-v2-GGUF)
